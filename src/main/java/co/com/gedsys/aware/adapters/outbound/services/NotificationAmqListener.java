package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.commons.events.task.TaskAssignedEvent;
import co.com.gedsys.commons.events.task.TaskCompletedEvent;
import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.NewNotificationEvent;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RabbitListener(queues = { QueueName.NOTIFICACIONES })
public class NotificationAmqListener {
        private final NotificationServices notificationServices;
        private final SimpMessagingTemplate simpMessagingTemplate;

        public NotificationAmqListener(NotificationServices notificationServices,
                        SimpMessagingTemplate simpMessagingTemplate) {
                this.notificationServices = notificationServices;
                this.simpMessagingTemplate = simpMessagingTemplate;
        }

        @RabbitHandler
        public void handleTaskAssigned(TaskAssignedEvent event) {
                log.info("Processing TaskAssignedEvent for task: {}", event.taskId());

                var notification = Notification.builder()
                                .owner(event.assignedTo())
                                .title(event.title())
                                .type(NotificationType.info)
                                .action(new Action(ActionType.link,
                                                String.format(UIPathConstants.VER_TAREA, event.taskFormKey(),
                                                                event.taskId()),
                                                ActionText.VER_TAREA.name()))
                                .status(NotificationStatus.unread)
                                .timestamp(LocalDateTime.now())
                                .details(event)
                                .build();

                notificationServices.saveNotification(notification);
                simpMessagingTemplate.convertAndSendToUser(event.assignedTo(), "/queue/notifications", notification);
        }

        @RabbitHandler
        public void handleTaskCompleted(TaskCompletedEvent event) {
                log.info("Processing TaskCompletedEvent for task: {}", event.taskId());

                var notification = Notification.builder()
                                .owner(event.completedBy())
                                .title(event.title())
                                .type(NotificationType.success)
                                .action(new Action(ActionType.link,
                                                String.format(UIPathConstants.VER_TAREA, event.taskFormKey(),
                                                                event.taskId()),
                                                ActionText.VER_TAREA.name()))
                                .status(NotificationStatus.unread)
                                .timestamp(LocalDateTime.now())
                                .details(event)
                                .build();

                notificationServices.saveNotification(notification);
                simpMessagingTemplate.convertAndSendToUser(event.completedBy(), "/queue/notifications", notification);
        }

        @RabbitHandler(isDefault = true)
        public void handleGenericNotification(NewNotificationEvent newNotification) {
                log.info("Processing generic NewNotificationEvent: {}", newNotification.title());

                var uniqueStakeholders = newNotification.stakeholders().stream().distinct().toList();

                for (String stakeholder : uniqueStakeholders) {
                        var notification = Notification.builder()
                                        .owner(stakeholder)
                                        .title(newNotification.title())
                                        .type(NotificationType.valueOf(newNotification.type()))
                                        .action(new Action(ActionType.valueOf(newNotification.actionType()),
                                                        newNotification.actionPath(),
                                                        newNotification.actionText()))
                                        .status(NotificationStatus.unread)
                                        .timestamp(LocalDateTime.now())
                                        .details(newNotification.details())
                                        .build();

                        notificationServices.saveNotification(notification);
                        simpMessagingTemplate.convertAndSendToUser(stakeholder, "/queue/notifications", notification);
                }
        }

}
