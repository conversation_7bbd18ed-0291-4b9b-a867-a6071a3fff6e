package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.events.NewNotificationEvent;
import co.com.gedsys.commons.interfaces.AbstractRabbitMQListener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;

@Slf4j
@Service
public class NotificationAmqListener extends AbstractRabbitMQListener<String> {
    private final ObjectMapper objectMapper;
    private final NotificationServices notificationServices;
    private final SimpMessagingTemplate simpMessagingTemplate;

    public NotificationAmqListener(RabbitTemplate rabbitTemplate,
            ObjectMapper objectMapper,
            NotificationServices notificationServices,
            SimpMessagingTemplate simpMessagingTemplate) {
        super(rabbitTemplate);
        this.objectMapper = objectMapper;
        this.notificationServices = notificationServices;
        this.simpMessagingTemplate = simpMessagingTemplate;
    }

    @RabbitListener(queues = { QueueName.NOTIFICACIONES }, containerFactory = "manualListenerContainerFactory")
    @Override
    public void processMessage(@Payload String payload, Message message, Channel channel,
            @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        super.processMessage(payload, message, channel, deliveryTag);
    }

    protected void handleMessageProcessing(String messageReceived) throws IOException {
        var newNotification = objectMapper.readValue(messageReceived, NewNotificationEvent.class);

        // Eliminar stakeholders duplicados
        var uniqueStakeholders = newNotification.stakeholders().stream().distinct().toList();

        for (String stakeholder : uniqueStakeholders) {
            var notification = Notification.builder()
                    .owner(stakeholder)
                    .title(newNotification.title())
                    .type(NotificationType.valueOf(newNotification.type()))
                    .action(new Action(ActionType.valueOf(newNotification.actionType()), newNotification.actionPath(),
                            newNotification.actionText()))
                    .status(NotificationStatus.unread)
                    .timestamp(LocalDateTime.now())
                    .details(newNotification.details())
                    .build();

            notificationServices.saveNotification(notification);
            simpMessagingTemplate.convertAndSendToUser(stakeholder, "/queue/notifications", notification);
        }
    }

}
