package co.com.gedsys.aware.infrastructure.config;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

/**
 * Configuración de colas y bindings para el manejo de notificaciones
 */
@Configuration
public class NotificacionQueueConfig {

    private static final String NOTIFICACIONES_DLQ = "notificaciones.dlq";
    private static final String NOTIFICACIONES_DLK = "notificaciones.dlk";

    @Bean
    Queue notificacionesDeadLetterQueue() {
        return QueueBuilder.durable(NOTIFICACIONES_DLQ)
                .withArgument("x-queue-type", "classic")
                .build();
    }

    @Bean
    @DependsOn({ "deadLetterExchange", "notificacionesDeadLetterQueue" })
    Binding notificacionesDeadLetterBinding(Queue notificacionesDeadLetterQueue,
            TopicExchange deadLetterExchange) {
        return BindingBuilder.bind(notificacionesDeadLetterQueue)
                .to(deadLetterExchange)
                .with(NOTIFICACIONES_DLK);
    }

    @Bean
    Queue notificacionNuevaQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES)
                .withArgument("x-queue-type", "classic")
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionesDocumentosProcesadosQueue" })
    Binding notificacionNuevaBinding(Queue notificacionesDocumentosProcesadosQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder.bind(notificacionesDocumentosProcesadosQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.NOTIFICACION_NUEVA);
    }

}
